import 'package:flutter/material.dart';
import 'package:focus_grid_client/constants/colors.dart';
import 'package:focus_grid_client/constants/game_constants.dart';
import 'package:focus_grid_client/providers/settings_provider.dart';
import 'package:focus_grid_client/screens/game_screen.dart';
import 'package:focus_grid_client/utils/audio_manager.dart';
import 'package:provider/provider.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  bool _settingsChanged = false;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('设置'), centerTitle: true, leading: BackButton(onPressed: () {
        Navigator.of(context).pop(_settingsChanged);
      })),
      body: Consumer<SettingsProvider>(
        builder: (context, settingsProvider, child) {
          return ListView(
            padding: const EdgeInsets.all(16),
            children: [
              // APP 简短介绍
              Padding(
                padding: const EdgeInsets.only(bottom: 24.0),
                child: Text(
                  '“专注方格”基于经典的舒尔特方格设计。\n舒尔特方格通过训练视野广度与注意力集中，能有效提升专注力、反应速度和视觉搜索能力，是提高学习与工作效率的经典工具。',
                  style: const TextStyle(fontSize: 16, color: Colors.black87),
                  textAlign: TextAlign.center,
                ),
              ),
              // 网格大小设置
              _buildSettingSection(
                title: '网格大小',
                child: Column(
                  children: [
                    Slider(
                      value: settingsProvider.settings.gridSize.toDouble(),
                      min: GameConstants.minGridSize.toDouble(),
                      max: GameConstants.maxGridSize.toDouble(),
                      divisions:
                          GameConstants.maxGridSize - GameConstants.minGridSize,
                      label:
                          '${settingsProvider.settings.gridSize}x${settingsProvider.settings.gridSize}',
                      onChanged: (value) {
                        settingsProvider.updateGridSize(value.round());
                        setState(() { _settingsChanged = true; });
                      },
                    ),
                    Text(
                      '${settingsProvider.settings.gridSize}x${settingsProvider.settings.gridSize}',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),


              // 提示模式设置
              _buildSettingSection(
                title: '提示模式',
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    RadioListTile<bool>(
                      title: const Text('标准提示（仅顶部数字，无颜色和边框）'),
                      value: false,
                      groupValue: settingsProvider.settings.showHints,
                      onChanged: (value) {
                        if (value != null) {
                          settingsProvider.updateShowHints(value);
                          setState(() { _settingsChanged = true; });
                        }
                      },
                    ),
                    RadioListTile<bool>(
                      title: const Text('引导提示（绿色标识+蓝色边框）'),
                      value: true,
                      groupValue: settingsProvider.settings.showHints,
                      onChanged: (value) {
                        if (value != null) {
                          settingsProvider.updateShowHints(value);
                          setState(() { _settingsChanged = true; });
                        }
                      },
                    ),
                  ],
                ),
              ),
              // 挑战模式时间限制
              if (settingsProvider.settings.mode == GameConstants.challengeMode)
                _buildSettingSection(
                  title: '挑战模式时间限制',
                  child: Column(
                    children: [
                      Slider(
                        value:
                            settingsProvider.settings.challengeTimeLimit
                                .toDouble(),
                        min: 10,
                        max: 60,
                        divisions: 5,
                        label:
                            '${settingsProvider.settings.challengeTimeLimit} 秒',
                        onChanged: (value) {
                          settingsProvider.updateChallengeTimeLimit(
                            value.round(),
                          );
                        },
                      ),
                      Text(
                        '${settingsProvider.settings.challengeTimeLimit} 秒',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),

              const SizedBox(height: 32),

            ],
          );
        },
      ),
    );
  }

  // 构建设置区块
  Widget _buildSettingSection({required String title, required Widget child}) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const Divider(),
            child,
          ],
        ),
      ),
    );
  }

  // 显示颜色选择器
  void _showColorPicker(
    BuildContext context,
    String title,
    Color initialColor,
    Function(Color) onColorChanged,
  ) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        Color selectedColor = initialColor;
        return AlertDialog(
          title: Text(title),
          content: SingleChildScrollView(
            child: Column(
              children: [
                // 这里只是一个简化的颜色选择界面
                // 实际应用中可以使用更复杂的颜色选择器插件
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _buildColorOption(Colors.red, selectedColor, (color) {
                      selectedColor = color;
                    }),
                    _buildColorOption(Colors.blue, selectedColor, (color) {
                      selectedColor = color;
                    }),
                    _buildColorOption(Colors.green, selectedColor, (color) {
                      selectedColor = color;
                    }),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _buildColorOption(Colors.yellow, selectedColor, (color) {
                      selectedColor = color;
                    }),
                    _buildColorOption(Colors.purple, selectedColor, (color) {
                      selectedColor = color;
                    }),
                    _buildColorOption(Colors.orange, selectedColor, (color) {
                      selectedColor = color;
                    }),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _buildColorOption(Colors.black, selectedColor, (color) {
                      selectedColor = color;
                    }),
                    _buildColorOption(Colors.white, selectedColor, (color) {
                      selectedColor = color;
                    }),
                    _buildColorOption(Colors.grey, selectedColor, (color) {
                      selectedColor = color;
                    }),
                  ],
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('取消'),
            ),
            ElevatedButton(
              onPressed: () {
                onColorChanged(selectedColor);
                Navigator.of(context).pop();
              },
              child: const Text('确定'),
            ),
          ],
        );
      },
    );
  }

  // 构建颜色选项
  Widget _buildColorOption(
    Color color,
    Color selectedColor,
    Function(Color) onSelect,
  ) {
    final isSelected = color.value == selectedColor.value;

    return GestureDetector(
      onTap: () => onSelect(color),
      child: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: color,
          border: Border.all(
            color: isSelected ? AppColors.primaryColor : Colors.grey,
            width: isSelected ? 3 : 1,
          ),
          borderRadius: BorderRadius.circular(4),
        ),
      ),
    );
  }
}
