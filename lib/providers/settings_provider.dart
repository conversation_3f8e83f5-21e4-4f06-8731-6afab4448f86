import 'package:flutter/material.dart';
import 'package:focus_grid_client/models/game_settings.dart';
import 'package:focus_grid_client/utils/storage_manager.dart';

class SettingsProvider extends ChangeNotifier {
  GameSettings _settings = GameSettings();
  ThemeMode _themeMode = ThemeMode.system;
  String _language = 'zh';

  SettingsProvider() {
    _loadSettings();
  }

  // 获取设置
  GameSettings get settings => _settings;
  ThemeMode get themeMode => _themeMode;
  String get language => _language;

  // 更新设置
  Future<void> updateSettings(GameSettings newSettings) async {
    _settings = newSettings;
    notifyListeners();
  }

  // 更新网格大小
  Future<void> updateGridSize(int size) async {
    _settings = _settings.copyWith(gridSize: size);
    notifyListeners();
  }

  // 更新游戏模式
  Future<void> updateGameMode(String mode) async {
    _settings = _settings.copyWith(mode: mode);
    notifyListeners();
  }

  // 更新使用字母设置
  Future<void> updateUseLetters(bool useLetters) async {
    _settings = _settings.copyWith(useLetters: useLetters);
    notifyListeners();
  }

  // 更新主题模式
  Future<void> updateThemeMode(ThemeMode mode) async {
    _themeMode = mode;
    _settings = _settings.copyWith(themeMode: mode);
    await StorageManager().saveThemeMode(mode);
    notifyListeners();
  }

  // 更新音效设置
  Future<void> updateSoundEnabled(bool enabled) async {
    _settings = _settings.copyWith(soundEnabled: enabled);
    notifyListeners();
  }

  // 更新震动设置
  Future<void> updateVibrationEnabled(bool enabled) async {
    _settings = _settings.copyWith(vibrationEnabled: enabled);
    notifyListeners();
  }

  // 更新挑战模式时间限制
  Future<void> updateChallengeTimeLimit(int seconds) async {
    _settings = _settings.copyWith(challengeTimeLimit: seconds);
    notifyListeners();
  }

  // 更新自定义颜色
  Future<void> updateColors({
    Color? backgroundColor,
    Color? cellColor,
    Color? textColor,
  }) async {
    _settings = _settings.copyWith(
      backgroundColor: backgroundColor,
      cellColor: cellColor,
      textColor: textColor,
    );
    notifyListeners();
  }

  // 更新语言
  Future<void> updateLanguage(String languageCode) async {
    _language = languageCode;
    await StorageManager().saveLanguage(languageCode);
    notifyListeners();
  }

  // 更新提示模式
  Future<void> updateShowHints(bool showHints) async {
    _settings = _settings.copyWith(showHints: showHints);
    notifyListeners();
  }

  // 加载保存的设置
  Future<void> _loadSettings() async {
    final ThemeMode savedThemeMode = await StorageManager().getThemeMode();
    final String savedLanguage = await StorageManager().getLanguage();

    _themeMode = savedThemeMode;
    _language = savedLanguage;
    _settings = _settings.copyWith(themeMode: savedThemeMode);

    notifyListeners();
  }
}
