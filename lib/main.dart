import 'package:flutter/material.dart';
import 'package:focus_grid_client/constants/themes.dart';
import 'package:focus_grid_client/providers/game_provider.dart';
import 'package:focus_grid_client/providers/settings_provider.dart';
import 'package:focus_grid_client/providers/stats_provider.dart';
import 'package:focus_grid_client/screens/game_screen.dart';
import 'package:focus_grid_client/screens/home_screen.dart';
import 'package:focus_grid_client/utils/audio_manager.dart';
import 'package:provider/provider.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  try {
    // 初始化音频管理器，但失败时不阻止应用启动
    await AudioManager().initialize();
  } catch (e) {
    debugPrint('音频初始化失败: $e');
  }

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (context) => SettingsProvider()),
        ChangeNotifierProvider(create: (context) => GameProvider()),
        ChangeNotifierProvider(create: (context) => StatsProvider()),
      ],
      child: Consumer<SettingsProvider>(
        builder: (context, settingsProvider, child) {
          return MaterialApp(
            title: 'FocusGrid',
            theme: AppThemes.lightTheme,
            darkTheme: AppThemes.darkTheme,
            themeMode: settingsProvider.themeMode,
            home: const Scaffold(  // 临时添加Scaffold确保基础结构
              body: Center(
                child: GameScreen(),
              ),
            ),
            debugShowCheckedModeBanner: false,
          );
        },
      ),
    );
  }
}
