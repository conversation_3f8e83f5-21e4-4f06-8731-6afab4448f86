import 'package:flutter/material.dart';
import 'package:focus_grid_client/models/game_record.dart';
import 'package:focus_grid_client/utils/storage_manager.dart';

class StatsProvider extends ChangeNotifier {
  List<GameRecord> _records = [];
  bool _isLoading = false;

  // Getter
  List<GameRecord> get records => _records;
  bool get isLoading => _isLoading;

  // 初始化，加载记录
  Future<void> loadRecords() async {
    _isLoading = true;
    notifyListeners();

    final rawRecords = await StorageManager().getGameRecords();
    _records = rawRecords.map((record) => GameRecord.fromJson(record)).toList();

    // 按时间戳排序，最新的在前面
    _records.sort((a, b) => b.timestamp.compareTo(a.timestamp));

    _isLoading = false;
    notifyListeners();
  }

  // 按模式筛选记录
  List<GameRecord> getRecordsByMode(String mode) {
    return _records.where((record) => record.mode == mode).toList();
  }

  // 按网格大小筛选记录
  List<GameRecord> getRecordsByGridSize(int size) {
    return _records.where((record) => record.gridSize == size).toList();
  }

  // 获取特定模式和网格大小的记录
  List<GameRecord> getFilteredRecords(String mode, int size) {
    return _records
        .where((record) => record.mode == mode && record.gridSize == size)
        .toList();
  }

  // 获取最佳记录
  GameRecord? getBestRecord(String mode, int size) {
    final filteredRecords = getFilteredRecords(mode, size);
    if (filteredRecords.isEmpty) {
      return null;
    }

    // 按完成时间排序，最快的在前面
    filteredRecords.sort(
      (a, b) => a.completionTime.inMilliseconds.compareTo(
        b.completionTime.inMilliseconds,
      ),
    );

    return filteredRecords.first;
  }

  // 获取平均用时
  Duration? getAverageTime(String mode, int size) {
    final filteredRecords = getFilteredRecords(mode, size);
    if (filteredRecords.isEmpty) {
      return null;
    }

    int totalMs = filteredRecords.fold(
      0,
      (sum, record) => sum + record.completionTime.inMilliseconds,
    );

    return Duration(milliseconds: totalMs ~/ filteredRecords.length);
  }

  // 获取最近一周的记录
  List<GameRecord> getLastWeekRecords() {
    final now = DateTime.now();
    final weekAgo = now.subtract(const Duration(days: 7));

    return _records
        .where((record) => record.timestamp.isAfter(weekAgo))
        .toList();
  }

  // 生成折线图数据 - 返回最近10条记录的用时数据 (毫秒)
  List<MapEntry<DateTime, int>> getChartData(String mode, int size) {
    final filteredRecords = getFilteredRecords(mode, size);

    // 按时间戳排序，最早的在前面
    filteredRecords.sort((a, b) => a.timestamp.compareTo(b.timestamp));

    // 取最近10条记录
    final recentRecords =
        filteredRecords.length > 10
            ? filteredRecords.sublist(filteredRecords.length - 10)
            : filteredRecords;

    return recentRecords
        .map(
          (record) =>
              MapEntry(record.timestamp, record.completionTime.inMilliseconds),
        )
        .toList();
  }
}
