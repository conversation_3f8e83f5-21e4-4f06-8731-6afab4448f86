# FocusGrid - 舒尔特方格训练应用

FocusGrid是一款基于舒尔特方格的专注力训练应用，可以帮助用户训练注意力、反应速度和眼动能力。

## 功能特点

- **多种游戏模式**：基础模式、挑战模式、自定义模式和放松模式
- **可定制的难度**：支持2x2到10x10不同大小的网格
- **数据统计分析**：记录完成时间、统计最佳成绩，展示进步趋势
- **自定义设置**：支持数字/字母切换、多种主题和颜色设置
- **音效反馈**：正确/错误反馈，提升游戏体验

## 开始使用

1. 确保已安装Flutter环境
2. 克隆本仓库
3. 添加必要的音效文件到 `assets/sounds/` 目录
   - correct.mp3 - 正确点击的声音
   - wrong.mp3 - 错误点击的声音
   - complete.mp3 - 完成游戏的声音
   - background.mp3 - 放松模式的背景音乐
4. 运行 `flutter pub get` 安装依赖
5. 运行 `flutter run` 启动应用

## 游戏说明

### 舒尔特方格是什么？

舒尔特方格是一种注意力训练工具，由德国心理学家沃尔特·舒尔特(Walter Schulte)开发。它通常是一个5×5的方格，内含1-25个随机排列的数字。训练者需要按照从小到大的顺序依次寻找并指出这些数字。这个过程可以训练人的注意力集中、眼部移动控制和视觉信息处理能力。

### 游戏模式

1. **基础模式**：按顺序找出所有数字，计时记录完成时间
2. **挑战模式**：在限定时间内完成尽可能多的网格，随进度增加难度
3. **自定义模式**：自定义网格大小、内容类型和界面颜色
4. **放松模式**：无时间压力，配有舒缓音乐，放松训练

## 技术栈

- Flutter/Dart
- Provider状态管理
- SharedPreferences数据持久化
- fl_chart图表展示
- audioplayers音频播放

## 贡献指南

欢迎提交问题或建议！如果您想为项目做出贡献，请遵循以下步骤：

1. Fork本仓库
2. 创建您的特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交您的更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启一个Pull Request
