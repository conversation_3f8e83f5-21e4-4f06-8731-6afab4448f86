import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/material.dart';
import 'package:focus_grid_client/constants/game_constants.dart';

class StorageManager {
  static final StorageManager _instance = StorageManager._internal();

  factory StorageManager() {
    return _instance;
  }

  StorageManager._internal();

  // 保存游戏记录
  Future<void> saveGameRecord(
    String mode,
    int gridSize,
    Duration completionTime,
  ) async {
    final prefs = await SharedPreferences.getInstance();

    // 获取现有记录
    List<Map<String, dynamic>> records = await getGameRecords();

    // 添加新记录
    records.add({
      'mode': mode,
      'gridSize': gridSize,
      'completionTime': completionTime.inMilliseconds,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    });

    // 如果记录过多，保留最近的记录
    if (records.length > GameConstants.maxRecordsToKeep) {
      records = records.sublist(
        records.length - GameConstants.maxRecordsToKeep,
      );
    }

    // 保存到 SharedPreferences
    await prefs.setString('game_records', jsonEncode(records));

    // 更新最佳记录
    await _updateBestScore(mode, gridSize, completionTime);
  }

  // 获取游戏记录
  Future<List<Map<String, dynamic>>> getGameRecords() async {
    final prefs = await SharedPreferences.getInstance();
    String? recordsJson = prefs.getString('game_records');

    if (recordsJson == null) {
      return [];
    }

    List<dynamic> decodedRecords = jsonDecode(recordsJson);
    return decodedRecords
        .map((record) => Map<String, dynamic>.from(record))
        .toList();
  }

  // 更新最佳分数
  Future<void> _updateBestScore(
    String mode,
    int gridSize,
    Duration completionTime,
  ) async {
    final prefs = await SharedPreferences.getInstance();
    String bestScoreKey = 'best_score_${mode}_${gridSize}';
    int? currentBest = prefs.getInt(bestScoreKey);

    if (currentBest == null || completionTime.inMilliseconds < currentBest) {
      await prefs.setInt(bestScoreKey, completionTime.inMilliseconds);
    }
  }

  // 获取最佳分数
  Future<Duration?> getBestScore(String mode, int gridSize) async {
    final prefs = await SharedPreferences.getInstance();
    String bestScoreKey = 'best_score_${mode}_${gridSize}';
    int? bestTime = prefs.getInt(bestScoreKey);

    if (bestTime == null) {
      return null;
    }

    return Duration(milliseconds: bestTime);
  }

  // 保存主题设置
  Future<void> saveThemeMode(ThemeMode themeMode) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt('theme_mode', themeMode.index);
  }

  // 获取主题设置
  Future<ThemeMode> getThemeMode() async {
    final prefs = await SharedPreferences.getInstance();
    int themeModeIndex = prefs.getInt('theme_mode') ?? ThemeMode.system.index;
    return ThemeMode.values[themeModeIndex];
  }

  // 保存语言设置
  Future<void> saveLanguage(String languageCode) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('language_code', languageCode);
  }

  // 获取语言设置
  Future<String> getLanguage() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('language_code') ?? 'zh'; // 默认中文
  }
}
