import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:focus_grid_client/constants/game_constants.dart';
import 'package:focus_grid_client/models/game_settings.dart';
import 'package:focus_grid_client/utils/audio_manager.dart';
import 'package:focus_grid_client/utils/grid_generator.dart';
import 'package:focus_grid_client/utils/storage_manager.dart';

class GameProvider extends ChangeNotifier {
  // 游戏状态
  bool _isPlaying = false;
  bool _isPaused = false;
  bool _isCompleted = false;

  // 当前网格
  List<List<dynamic>> _grid = [];

  // 当前目标数字或字母
  int _currentTarget = 1;
  String _currentLetterTarget = 'A';

  // 计时器
  int _elapsedTimeInMs = 0;
  Timer? _timer;

  // 挑战模式计时器
  int _challengeRemainingTimeInMs = 0;
  Timer? _challengeTimer;

  // 设置
  GameSettings _settings = GameSettings();

  // 点击记录
  Set<String> _clickedCells = {};

  // Getters
  bool get isPlaying => _isPlaying;
  bool get isPaused => _isPaused;
  bool get isCompleted => _isCompleted;
  List<List<dynamic>> get grid => _grid;
  int get currentTarget => _currentTarget;
  String get currentLetterTarget => _currentLetterTarget;
  int get elapsedTimeInMs => _elapsedTimeInMs;
  int get challengeRemainingTimeInMs => _challengeRemainingTimeInMs;
  GameSettings get settings => _settings;
  Set<String> get clickedCells => _clickedCells;

  /// 新增 nextTarget getter，根据当前模式返回下一个目标（数字或字母）
  dynamic get nextTarget {
    if (_settings.mode == GameConstants.letterMode) {
      return _currentLetterTarget;
    } else {
      return _currentTarget;
    }
  }
  // 格式化时间
  String get formattedTime {
    int minutes = (_elapsedTimeInMs ~/ 60000);
    int seconds = (_elapsedTimeInMs ~/ 1000) % 60;
    int milliseconds = (_elapsedTimeInMs % 1000) ~/ 10;

    return '$minutes:${seconds.toString().padLeft(2, '0')}.${milliseconds.toString().padLeft(2, '0')}';
  }

  // 挑战模式剩余时间
  String get formattedChallengeTime {
    int seconds = (_challengeRemainingTimeInMs ~/ 1000);
    return '$seconds 秒';
  }

  // 初始化游戏
  void initGame(GameSettings settings) {
    _settings = settings;
    _isPlaying = false;
    _isPaused = false;
    _isCompleted = false;
    _elapsedTimeInMs = 0;
    _challengeRemainingTimeInMs = settings.challengeTimeLimit * 1000;
    _currentTarget = 1;
    _currentLetterTarget = 'A';
    _clickedCells = {};

    _generateGrid();

    notifyListeners();
  }

  // 生成网格
  void _generateGrid() {
    if (_settings.useLetters) {
      _grid = GridGenerator.generateLetterGrid(_settings.gridSize);
    } else {
      if (_settings.mode == GameConstants.challengeMode) {
        _grid = GridGenerator.generateChallengeGrid(_settings.gridSize);
      } else {
        _grid = GridGenerator.generateNumberGrid(_settings.gridSize);
      }
    }
  }

  // 开始游戏
  void startGame() {
    if (!_isPlaying) {
      _isPlaying = true;
      _isPaused = false;
      _startTimer();

      // 如果是放松模式，播放背景音乐
      if (_settings.mode == GameConstants.relaxMode && _settings.soundEnabled) {
        AudioManager().playBackground();
      }

      notifyListeners();
    }
  }

  // 暂停游戏
  void pauseGame() {
    if (_isPlaying && !_isPaused) {
      _isPaused = true;
      _timer?.cancel();
      _challengeTimer?.cancel();
      notifyListeners();
    }
  }

  // 恢复游戏
  void resumeGame() {
    if (_isPlaying && _isPaused) {
      _isPaused = false;
      _startTimer();
      notifyListeners();
    }
  }

  // 重置游戏
  void resetGame() {
    _timer?.cancel();
    _challengeTimer?.cancel();
    initGame(_settings);
  }

  // 处理网格点击
  void handleCellTap(int row, int col) {
    if (!_isPlaying || _isPaused || _isCompleted) {
      return;
    }

    final cellKey = '$row-$col';
    final dynamic cellValue = _grid[row][col];

    // 已点击过的单元格
    if (_clickedCells.contains(cellKey)) {
      return;
    }

    // 检查是否正确点击
    bool isCorrect = false;

    if (_settings.useLetters) {
      // 字母模式
      final int charCodeA = 'A'.codeUnitAt(0);
      final int targetIndex = _currentLetterTarget.codeUnitAt(0) - charCodeA;
      isCorrect = cellValue == _currentLetterTarget;

      if (isCorrect) {
        _clickedCells.add(cellKey);

        // 检查是否完成
        if (targetIndex + 1 >= _settings.gridSize * _settings.gridSize) {
          _completeGame();
        } else {
          // 更新下一个目标
          _currentLetterTarget = String.fromCharCode(
            charCodeA + targetIndex + 1,
          );
          AudioManager().playCorrect();
        }
      } else {
        AudioManager().playWrong();
        if (_settings.vibrationEnabled) {
          HapticFeedback.vibrate();
        }
      }
    } else {
      // 数字模式
      int value = cellValue;
      if (value < 0) {
        // 挑战模式中的干扰单元格
        value = -value;
      }

      isCorrect = value == _currentTarget;

      if (isCorrect) {
        _clickedCells.add(cellKey);

        // 检查是否完成
        if (_currentTarget >= _settings.gridSize * _settings.gridSize) {
          _completeGame();
        } else {
          // 更新下一个目标
          _currentTarget++;
          AudioManager().playCorrect();
        }
      } else {
        AudioManager().playWrong();
        if (_settings.vibrationEnabled) {
          HapticFeedback.vibrate();
        }
      }
    }

    notifyListeners();
  }

  // 完成游戏
  void _completeGame() {
    _isCompleted = true;
    _timer?.cancel();
    _challengeTimer?.cancel();

    // 播放完成音效
    AudioManager().playComplete();

    // 保存游戏记录
    final completionTime = Duration(milliseconds: _elapsedTimeInMs);
    StorageManager().saveGameRecord(
      _settings.mode,
      _settings.gridSize,
      completionTime,
    );

    notifyListeners();
  }

  // 开始计时器
  void _startTimer() {
    // 主计时器
    _timer = Timer.periodic(const Duration(milliseconds: 10), (timer) {
      if (!_isCompleted) {
        _elapsedTimeInMs += 10;
        notifyListeners();
      }
    });

    // 挑战模式倒计时
    if (_settings.mode == GameConstants.challengeMode) {
      _challengeTimer = Timer.periodic(const Duration(milliseconds: 10), (
        timer,
      ) {
        _challengeRemainingTimeInMs -= 10;

        // 时间耗尽
        if (_challengeRemainingTimeInMs <= 0) {
          _challengeRemainingTimeInMs = 0;
          timer.cancel();
          _timer?.cancel();
          _isCompleted = true;
          notifyListeners();
        }
      });
    }
  }

  // 获取单元格状态
  CellState getCellState(int row, int col) {
    final cellKey = '$row-$col';

    if (_clickedCells.contains(cellKey)) {
      return CellState.clicked;
    }

    // 挑战模式中的干扰格子
    if (!_settings.useLetters &&
        _settings.mode == GameConstants.challengeMode) {
      if (_grid[row][col] < 0) {
        return CellState.distraction;
      }
    }

    return CellState.normal;
  }

  // 释放资源
  @override
  void dispose() {
    _timer?.cancel();
    _challengeTimer?.cancel();
    super.dispose();
  }

  // 停止计时器（公开方法）
  void stopTimer() {
    _timer?.cancel();
    _challengeTimer?.cancel();
    notifyListeners();
  }
}

enum CellState { normal, clicked, distraction }
