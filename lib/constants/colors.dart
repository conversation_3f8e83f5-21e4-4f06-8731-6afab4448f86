import 'package:flutter/material.dart';

class AppColors {
  // 主题颜色
  static const Color primaryColor = Color(0xFF2196F3);
  static const Color primaryLightColor = Color(0xFF42A5F5);
  static const Color primaryDarkColor = Color(0xFF1E2A44);

  // 背景颜色
  static const Color lightBackground = Color(0xFFF5F5F5);
  static const Color darkBackground = Color(0xFF1E2A44);

  // 文字颜色
  static const Color textColor = Color(0xFF000000);
  static const Color textLightColor = Color(0xFFFFFFFF);

  // 状态颜色
  static const Color correctColor = Color(0xFF4CAF50);
  static const Color wrongColor = Color(0xFFF44336);
  static const Color neutralColor = Color(0xFF9E9E9E);

  // 按钮颜色
  static const Color buttonColor = Color(0xFF2196F3);
  static const Color buttonLightColor = Color(0xFF42A5F5);

  // 方格颜色
  static const List<Color> gridColors = [Color(0xFFFAFAFA), Color(0xFFE0E0E0)];
}
