import 'package:flutter/material.dart';
import 'package:focus_grid_client/constants/colors.dart';
import 'package:focus_grid_client/providers/game_provider.dart';
import 'package:focus_grid_client/utils/storage_manager.dart';

class CompletionDialog extends StatefulWidget {
  final GameProvider gameProvider;

  const CompletionDialog({super.key, required this.gameProvider});

  @override
  State<CompletionDialog> createState() => _CompletionDialogState();
}

class _CompletionDialogState extends State<CompletionDialog> {
  bool _isLoading = true;
  Duration? _bestTime;

  @override
  void initState() {
    super.initState();
    _loadBestTime();
  }

  Future<void> _loadBestTime() async {
    final bestTime = await StorageManager().getBestScore(
      widget.gameProvider.settings.mode,
      widget.gameProvider.settings.gridSize,
    );

    setState(() {
      _bestTime = bestTime;
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    final completionTime = Duration(
      milliseconds: widget.gameProvider.elapsedTimeInMs,
    );
    final isNewBest = _bestTime != null && completionTime <= _bestTime!;

    return AlertDialog(
      title: Row(
        children: [
          const Icon(Icons.emoji_events, color: Colors.amber),
          const SizedBox(width: 8),
          const Text('恭喜完成!'),
        ],
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildResultRow('完成时间', _formatTime(completionTime)),

          const SizedBox(height: 16),

          if (_isLoading)
            const CircularProgressIndicator()
          else if (_bestTime != null)
            _buildResultRow(
              '最佳记录',
              _formatTime(_bestTime!),
              isHighlighted: isNewBest,
            ),

          if (isNewBest) ...[
            const SizedBox(height: 16),
            const Text(
              '🎉 新的最佳记录! 🎉',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: AppColors.correctColor,
              ),
            ),
          ],
        ],
      ),
      actions: [
        TextButton(
          onPressed: () {
            Navigator.of(context).pop();
            widget.gameProvider.resetGame();
          },
          child: const Text('再玩一次'),
        ),
        ElevatedButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('返回'),
        ),
      ],
    );
  }

  Widget _buildResultRow(
    String label,
    String value, {
    bool isHighlighted = false,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: isHighlighted ? AppColors.correctColor : null,
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: isHighlighted ? AppColors.correctColor : null,
          ),
        ),
      ],
    );
  }

  String _formatTime(Duration duration) {
    int minutes = duration.inMinutes;
    int seconds = duration.inSeconds % 60;
    int milliseconds = duration.inMilliseconds % 1000;

    return '$minutes:${seconds.toString().padLeft(2, '0')}.${(milliseconds ~/ 10).toString().padLeft(2, '0')}';
  }
}
