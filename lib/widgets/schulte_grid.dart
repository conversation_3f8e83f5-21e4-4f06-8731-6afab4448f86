import 'package:flutter/material.dart';
import 'package:focus_grid_client/providers/game_provider.dart';
import 'package:focus_grid_client/widgets/grid_cell.dart';
import 'package:provider/provider.dart';

class SchulteGrid extends StatelessWidget {
  const SchulteGrid({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<GameProvider>(
      builder: (context, gameProvider, child) {
        final grid = gameProvider.grid;

        // 如果网格为空，显示加载中
        if (grid.isEmpty) {
          return const Center(child: CircularProgressIndicator());
        }

        final size = grid.length; // 网格大小 (例如 5x5)
        final useLetters = gameProvider.settings.useLetters;

        return AspectRatio(
          aspectRatio: 1, // 保持网格为正方形
          child: Consumer<SettingsProvider>(
            builder: (context, settingsProvider, _) {
              final showHints = settingsProvider.settings.showHints;
              return GridView.builder(
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: size,
                  childAspectRatio: 1,
                ),
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: size * size,
                itemBuilder: (context, index) {
                  final row = index ~/ size;
                  final col = index % size;
                  final cellValue = grid[row][col];
                  final cellState = gameProvider.getCellState(row, col);

                  // 确定当前方格是否是目标值
                  bool isCurrentTarget = false;
                  if (useLetters) {
                    isCurrentTarget =
                        cellValue.toString() == gameProvider.currentLetterTarget;
                  } else {
                    dynamic targetValue = cellValue;
                    if (cellValue is int && cellValue < 0) {
                      targetValue = -cellValue;
                    }
                    isCurrentTarget = targetValue == gameProvider.currentTarget;
                  }

                  return GridCell(
                    value:
                        cellValue is int && cellValue < 0 ? -cellValue : cellValue,
                    state: cellState,
                    isCurrentTarget:
                        showHints && isCurrentTarget && !gameProvider.clickedCells.contains('$row-$col'),
                    onTap: () => gameProvider.handleCellTap(row, col),
                    backgroundColor: _getCellColor(row, col, size),
                    gridSize: size,
                    showHints: showHints,
                  );
                },
              );
            },
          ),
        );
      },
    );
  }

  // 获取单元格的交替颜色，创建棋盘格效果
  Color _getCellColor(int row, int col, int size) {
    if ((row + col) % 2 == 0) {
      return const Color(0xFFFAFAFA);
    } else {
      return const Color(0xFFF0F0F0);
    }
  }
}
