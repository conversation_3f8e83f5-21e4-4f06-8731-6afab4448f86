import 'dart:math';

class GridGenerator {
  // 生成随机数字网格
  static List<List<int>> generateNumberGrid(int size) {
    final int totalCells = size * size;
    List<int> numbers = List.generate(totalCells, (index) => index + 1);
    numbers.shuffle(Random());

    List<List<int>> grid = [];
    for (int i = 0; i < size; i++) {
      List<int> row = [];
      for (int j = 0; j < size; j++) {
        row.add(numbers[i * size + j]);
      }
      grid.add(row);
    }

    return grid;
  }

  // 生成随机字母网格
  static List<List<String>> generateLetterGrid(int size) {
    const String letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    final int totalCells = size * size;

    if (totalCells > letters.length) {
      throw Exception('网格大小超出可用字母数量');
    }

    List<String> letterList = letters.substring(0, totalCells).split('');
    letterList.shuffle(Random());

    List<List<String>> grid = [];
    for (int i = 0; i < size; i++) {
      List<String> row = [];
      for (int j = 0; j < size; j++) {
        row.add(letterList[i * size + j]);
      }
      grid.add(row);
    }

    return grid;
  }

  // 生成带干扰的网格（供挑战模式使用）
  static List<List<int>> generateChallengeGrid(
    int size, {
    int distractionCount = 2,
  }) {
    List<List<int>> grid = generateNumberGrid(size);
    Random random = Random();

    // 添加干扰位置标记（在游戏逻辑中使用）
    for (int i = 0; i < distractionCount; i++) {
      int row = random.nextInt(size);
      int col = random.nextInt(size);
      // 在实际游戏中，可以用负数标记干扰位置
      // 这里仅作示例，具体实现可能会有所不同
      grid[row][col] = -grid[row][col];
    }

    return grid;
  }
}
