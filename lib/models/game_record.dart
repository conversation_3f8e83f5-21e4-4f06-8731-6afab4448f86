class GameRecord {
  final String mode;
  final int gridSize;
  final Duration completionTime;
  final DateTime timestamp;

  GameRecord({
    required this.mode,
    required this.gridSize,
    required this.completionTime,
    required this.timestamp,
  });

  factory GameRecord.fromJson(Map<String, dynamic> json) {
    return GameRecord(
      mode: json['mode'],
      gridSize: json['gridSize'],
      completionTime: Duration(milliseconds: json['completionTime']),
      timestamp: DateTime.fromMillisecondsSinceEpoch(json['timestamp']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'mode': mode,
      'gridSize': gridSize,
      'completionTime': completionTime.inMilliseconds,
      'timestamp': timestamp.millisecondsSinceEpoch,
    };
  }

  // 转换为可读格式的时间
  String get formattedTime {
    int minutes = completionTime.inMinutes;
    int seconds = completionTime.inSeconds % 60;
    int milliseconds = completionTime.inMilliseconds % 1000;

    return '$minutes:${seconds.toString().padLeft(2, '0')}.${(milliseconds ~/ 10).toString().padLeft(2, '0')}';
  }

  // 格式化日期
  String get formattedDate {
    return '${timestamp.year}-${timestamp.month.toString().padLeft(2, '0')}-${timestamp.day.toString().padLeft(2, '0')} ${timestamp.hour.toString().padLeft(2, '0')}:${timestamp.minute.toString().padLeft(2, '0')}';
  }
}
