import 'package:flutter/material.dart';
import 'package:focus_grid_client/constants/colors.dart';
import 'package:focus_grid_client/providers/game_provider.dart';

class GridCell extends StatelessWidget {
  final dynamic value;
  final CellState state;
  final VoidCallback onTap;
  final Color textColor;
  final Color backgroundColor;
  final bool isCurrentTarget;
  final int gridSize;
  final bool showHints;

  const GridCell({
    super.key,
    required this.value,
    required this.state,
    required this.onTap,
    this.textColor = AppColors.textColor,
    this.backgroundColor = Colors.white,
    this.isCurrentTarget = false,
    required this.gridSize,
    required this.showHints,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        margin: const EdgeInsets.all(4),
        decoration: BoxDecoration(
          color: _getBackgroundColor(),
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: Colors.black12,
              blurRadius: 2,
              offset: const Offset(0, 1),
            ),
          ],
          border:
              showHints && isCurrentTarget
                  ? Border.all(color: AppColors.primaryColor, width: 2.0)
                  : null,
        ),
        child: Center(
          child: Text(
            value.toString(),
            style: TextStyle(
              fontSize: _getFontSize(),
              fontWeight: FontWeight.bold,
              color: _getTextColor(),
            ),
          ),
        ),
      ),
    );
  }

  // 根据状态获取背景颜色
  Color _getBackgroundColor() {
    if (!showHints) return backgroundColor;
    switch (state) {
      case CellState.clicked:
        return AppColors.correctColor.withOpacity(0.3);
      case CellState.distraction:
        return Colors.orange.withOpacity(0.3);
      case CellState.normal:
      default:
        return backgroundColor;
    }
  }

  // 根据状态获取文字颜色
  Color _getTextColor() {
    switch (state) {
      case CellState.clicked:
        return AppColors.correctColor;
      case CellState.distraction:
        return Colors.orange;
      case CellState.normal:
      default:
        return textColor;
    }
  }
  // 动态计算字体大小，方格越多字体越小
  double _getFontSize() {
    // 这里24是经验值，可以根据实际效果调整
    return 120.0 / gridSize;
  }
}
