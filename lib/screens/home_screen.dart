import 'package:flutter/material.dart';
import 'package:focus_grid_client/constants/colors.dart';
import 'package:focus_grid_client/constants/game_constants.dart';
import 'package:focus_grid_client/models/game_settings.dart';
import 'package:focus_grid_client/providers/settings_provider.dart';
import 'package:focus_grid_client/screens/game_screen.dart';
import 'package:focus_grid_client/screens/settings_screen.dart';
import 'package:focus_grid_client/screens/stats_screen.dart';
import 'package:provider/provider.dart';

class HomeScreen extends StatelessWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    // 进入首页后直接倒计时3秒进入基础模式
    Future.delayed(Duration.zero, () {
      final settingsProvider = Provider.of<SettingsProvider>(context, listen: false);
      settingsProvider.updateGameMode(GameConstants.basicMode);
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(builder: (context) => const GameScreen()),
      );
    });
    return Scaffold(
      body: Container(),
    );
  }
}

  Widget _buildModeCard({
    required BuildContext context,
    required String title,
    required String description,
    required IconData icon,
    required Color color,
    required String mode,
  }) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      elevation: 4,
      child: InkWell(
        onTap: () => _navigateToGameScreen(context, mode),
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // 图标和颜色区域
              Container(
                width: 64,
                height: 64,
                decoration: BoxDecoration(
                  color: color.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(icon, size: 36, color: color),
              ),

              const SizedBox(width: 16),

              // 标题和描述
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      description,
                      style: const TextStyle(
                        fontSize: 14,
                        color: AppColors.neutralColor,
                      ),
                    ),
                  ],
                ),
              ),

              // 箭头图标
              const Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: AppColors.neutralColor,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _navigateToGameScreen(BuildContext context, String mode) {
    final settingsProvider = Provider.of<SettingsProvider>(
      context,
      listen: false,
    );

    // 根据模式更新设置
    settingsProvider.updateGameMode(mode);

    if (mode == GameConstants.customMode) {
      // 自定义模式先导航到设置屏幕
      Navigator.push(
        context,
        MaterialPageRoute(builder: (context) => const SettingsScreen()),
      );
    } else {
      // 直接导航到游戏屏幕
      Navigator.push(
        context,
        MaterialPageRoute(builder: (context) => const GameScreen()),
      );
    }
  }
