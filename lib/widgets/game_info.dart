import 'package:flutter/material.dart';
import 'package:focus_grid_client/constants/colors.dart';
import 'package:focus_grid_client/constants/game_constants.dart';
import 'package:focus_grid_client/providers/game_provider.dart';
import 'package:provider/provider.dart';

class GameInfo extends StatefulWidget {
  const GameInfo({super.key});

  @override
  State<GameInfo> createState() => _GameInfoState();
}

class _GameInfoState extends State<GameInfo> {
  int _countdown = 0;
  bool _isCountingDown = false;
  @override
  void dispose() {
    super.dispose();
  }

  void _startRestartCountdown(GameProvider gameProvider) {
    setState(() {
      _countdown = 3;
      _isCountingDown = true;
    });
    Future.doWhile(() async {
      await Future.delayed(const Duration(seconds: 1));
      if (_countdown > 1) {
        setState(() {
          _countdown--;
        });
        return true;
      } else {
        setState(() {
          _isCountingDown = false;
        });
        // 倒计时结束后重启游戏
        gameProvider.resetGame();
        gameProvider.initGame(gameProvider.settings);
        gameProvider.startGame();
        return false;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<GameProvider>(
      builder: (context, gameProvider, child) {
        final isPlaying = gameProvider.isPlaying;
        final isPaused = gameProvider.isPaused;
        final isCompleted = gameProvider.isCompleted;
        final useLetters = gameProvider.settings.useLetters;
        final mode = gameProvider.settings.mode;

        String targetText = '准备开始';
        if (isPlaying && !isCompleted) {
          if (useLetters) {
            targetText = '请点击: ${gameProvider.currentLetterTarget}';
          } else {
            targetText = '请点击: ${gameProvider.currentTarget}';
          }
        } else if (isCompleted) {
          targetText = '已完成!';
        }

        return Container(
          padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black12,
                blurRadius: 5,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            children: [
              // 只保留用时显示
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      const Text(
                        '用时',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.black54,
                        ),
                      ),
                      SizedBox(width: 12),
                      Text(
                        gameProvider.formattedTime,
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Colors.black,
                        ),
                      ),
                    ],
                  ),
                  _isCountingDown
                      ? Container(
                          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
                          decoration: BoxDecoration(
                            color: Colors.grey.shade200,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            '$_countdown',
                            style: const TextStyle(fontSize: 18, color: Colors.grey, fontWeight: FontWeight.bold),
                          ),
                        )
                      : OutlinedButton(
                          onPressed: () {
                            if (!gameProvider.isPlaying && !gameProvider.isPaused && !gameProvider.isCompleted) return;
                            if (_isCountingDown) return;
                            gameProvider.stopTimer();
                            _startRestartCountdown(gameProvider);
                          },
                          style: OutlinedButton.styleFrom(
                            side: BorderSide(color: Colors.grey.shade300),
                            foregroundColor: Colors.grey.shade600,
                            backgroundColor: Colors.transparent,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                            textStyle: const TextStyle(fontSize: 13),
                          ),
                          child: const Text(
                            '重新开始',
                            style: TextStyle(fontSize: 13, color: Colors.grey),
                          ),
                        ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  // 获取模式对应的颜色
  Color _getModeColor(String mode) {
    switch (mode) {
      case GameConstants.basicMode:
        return AppColors.primaryColor;
      case GameConstants.challengeMode:
        return Colors.orange;
      case GameConstants.customMode:
        return Colors.purple;
      case GameConstants.relaxMode:
        return Colors.teal;
      default:
        return AppColors.primaryColor;
    }
  }

  // 获取模式对应的文本
  String _getModeText(String mode) {
    switch (mode) {
      case GameConstants.basicMode:
        return '基础模式';
      case GameConstants.challengeMode:
        return '挑战模式';
      case GameConstants.customMode:
        return '自定义模式';
      case GameConstants.relaxMode:
        return '放松模式';
      default:
        return '未知模式';
    }
  }
}
