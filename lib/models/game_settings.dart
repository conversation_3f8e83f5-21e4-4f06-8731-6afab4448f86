import 'package:flutter/material.dart';

class GameSettings {
  // 网格大小
  final int gridSize;

  // 游戏模式
  final String mode;

  // 是否使用字母而不是数字
  final bool useLetters;

  // 主题设置
  final ThemeMode themeMode;

  // 音效设置
  final bool soundEnabled;
  final bool vibrationEnabled;

  // 挑战模式设置
  final int challengeTimeLimit; // 秒

  // 自定义模式颜色设置
  final Color backgroundColor;
  final Color cellColor;
  final Color textColor;

  // 是否显示提示（true: 颜色和边框提示，false: 仅顶部数字提示）
  final bool showHints;

  GameSettings({
    this.gridSize = 5,
    this.mode = 'basic',
    this.useLetters = false,
    this.themeMode = ThemeMode.system,
    this.soundEnabled = true,
    this.vibrationEnabled = true,
    this.challengeTimeLimit = 30,
    this.backgroundColor = const Color(0xFFF5F5F5),
    this.cellColor = const Color(0xFFFFFFFF),
    this.textColor = const Color(0xFF000000),
    this.showHints = true,
  });

  // 创建一个设置的副本并修改部分属性
  GameSettings copyWith({
    int? gridSize,
    String? mode,
    bool? useLetters,
    ThemeMode? themeMode,
    bool? soundEnabled,
    bool? vibrationEnabled,
    int? challengeTimeLimit,
    Color? backgroundColor,
    Color? cellColor,
    Color? textColor,
    bool? showHints,
  }) {
    return GameSettings(
      gridSize: gridSize ?? this.gridSize,
      mode: mode ?? this.mode,
      useLetters: useLetters ?? this.useLetters,
      themeMode: themeMode ?? this.themeMode,
      soundEnabled: soundEnabled ?? this.soundEnabled,
      vibrationEnabled: vibrationEnabled ?? this.vibrationEnabled,
      challengeTimeLimit: challengeTimeLimit ?? this.challengeTimeLimit,
      backgroundColor: backgroundColor ?? this.backgroundColor,
      cellColor: cellColor ?? this.cellColor,
      textColor: textColor ?? this.textColor,
      showHints: showHints ?? this.showHints,
    );
  }
}
