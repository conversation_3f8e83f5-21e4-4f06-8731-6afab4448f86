import 'package:audioplayers/audioplayers.dart';
import 'package:shared_preferences/shared_preferences.dart';

class AudioManager {
  static final AudioManager _instance = AudioManager._internal();

  factory AudioManager() {
    return _instance;
  }

  AudioManager._internal();

  final AudioPlayer _correctPlayer = AudioPlayer();
  final AudioPlayer _wrongPlayer = AudioPlayer();
  final AudioPlayer _completePlayer = AudioPlayer();
  final AudioPlayer _backgroundPlayer = AudioPlayer();

  bool _soundEnabled = true;
  bool _vibrationEnabled = true;

  // 初始化音效
  Future<void> initialize() async {
    final prefs = await SharedPreferences.getInstance();
    _soundEnabled = prefs.getBool('sound_enabled') ?? true;
    _vibrationEnabled = prefs.getBool('vibration_enabled') ?? true;

    // 音效资源初始化
    await _correctPlayer.setSource(AssetSource('sounds/correct.mp3'));
    await _wrongPlayer.setSource(AssetSource('sounds/wrong.mp3'));
    await _completePlayer.setSource(AssetSource('sounds/complete.mp3'));
    await _backgroundPlayer.setSource(AssetSource('sounds/background.mp3'));

    // 背景音乐设置为循环播放
    await _backgroundPlayer.setReleaseMode(ReleaseMode.loop);
  }

  // 获取和设置状态
  bool get isSoundEnabled => _soundEnabled;
  bool get isVibrationEnabled => _vibrationEnabled;

  Future<void> setSoundEnabled(bool enabled) async {
    _soundEnabled = enabled;
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('sound_enabled', enabled);

    if (!enabled) {
      stopBackground();
    }
  }

  Future<void> setVibrationEnabled(bool enabled) async {
    _vibrationEnabled = enabled;
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('vibration_enabled', enabled);
  }

  // 音效播放方法
  void playCorrect() {
    if (_soundEnabled) {
      _correctPlayer.resume();
    }
  }

  void playWrong() {
    if (_soundEnabled) {
      _wrongPlayer.resume();
    }
  }

  void playComplete() {
    if (_soundEnabled) {
      _completePlayer.resume();
    }
  }

  void playBackground() {
    if (_soundEnabled) {
      _backgroundPlayer.resume();
    }
  }

  void stopBackground() {
    _backgroundPlayer.pause();
  }

  // 释放资源
  void dispose() {
    _correctPlayer.dispose();
    _wrongPlayer.dispose();
    _completePlayer.dispose();
    _backgroundPlayer.dispose();
  }
}
