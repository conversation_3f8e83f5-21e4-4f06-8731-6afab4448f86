import 'dart:async';

import 'package:flutter/material.dart';
import 'package:focus_grid_client/providers/game_provider.dart';
import 'package:focus_grid_client/providers/settings_provider.dart';
import 'package:focus_grid_client/screens/settings_screen.dart';
import 'package:focus_grid_client/screens/stats_screen.dart';
import 'package:focus_grid_client/widgets/completion_dialog.dart';
import 'package:focus_grid_client/widgets/game_info.dart';
import 'package:focus_grid_client/widgets/schulte_grid.dart';
import 'package:provider/provider.dart';

class GameScreen extends StatefulWidget {
  const GameScreen({super.key});

  @override
  State<GameScreen> createState() => GameScreenState();
}

class GameScreenState extends State<GameScreen> {
  int _countdown = 3;
  bool _showCountdown = true;
  Timer? _timer;

  @override
  void initState() {
    super.initState();
    _startCountdown();
  }

  void _startCountdown() {
    setState(() {
      _countdown = 3;
      _showCountdown = true;
    });
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_countdown == 1) {
        timer.cancel();
        setState(() {
          _showCountdown = false;
        });
        // 倒计时结束后初始化游戏
        WidgetsBinding.instance.addPostFrameCallback((_) {
          final gameProvider = Provider.of<GameProvider>(context, listen: false);
          final settingsProvider = Provider.of<SettingsProvider>(context, listen: false);
          gameProvider.initGame(settingsProvider.settings);
          // 倒计时结束后立即开始游戏和计时
          gameProvider.startGame();
        });
      } else {
        setState(() {
          _countdown--;
        });
      }
    });
  }

  void restartGameWithCountdown() {
    _timer?.cancel();
    setState(() {
      _countdown = 3;
      _showCountdown = true;
    });
    // 倒计时期间不做任何与游戏状态相关的操作
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_countdown == 1) {
        timer.cancel();
        setState(() {
          _showCountdown = false;
        });
        // 只在倒计时结束后再初始化和启动游戏
        WidgetsBinding.instance.addPostFrameCallback((_) {
          final gameProvider = Provider.of<GameProvider>(context, listen: false);
          final settingsProvider = Provider.of<SettingsProvider>(context, listen: false);
          // 先重置游戏（包括用时），再初始化和启动
          gameProvider.resetGame();
          gameProvider.initGame(settingsProvider.settings);
          gameProvider.startGame();
        });
      } else {
        setState(() {
          _countdown--;
        });
      }
    });
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: _onWillPop,
      child: Scaffold(
        appBar: AppBar(
          leading: IconButton(
            icon: const Icon(Icons.bar_chart),
            tooltip: '历史记录',
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const StatsScreen()),
              );
            },
          ),
          title: const Text('专注方格'),
          centerTitle: true,
          actions: [
            IconButton(
              icon: const Icon(Icons.settings),
              tooltip: '设置',
              onPressed: () async {
                final result = await Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => const SettingsScreen()),
                );
                if (result == true) {
                  restartGameWithCountdown();
                }
              },
            ),
          ],
        ),
        body: Stack(
          children: [
            Consumer<GameProvider>(
              builder: (context, gameProvider, child) {
                if (gameProvider.isCompleted && !_showCountdown) {
                  WidgetsBinding.instance.addPostFrameCallback((_) {
                    _showCompletionDialog(context, gameProvider);
                  });
                }
                return SafeArea(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      children: [
                        // 顶部提示区块，根据 showHints 决定显示内容
                        Consumer<SettingsProvider>(
                          builder: (context, settingsProvider, child) {
                            final showHints = settingsProvider.settings.showHints;
                            final gameProvider = Provider.of<GameProvider>(context, listen: false);
                            return Column(
                              children: [
                                const GameInfo(),
                                if (!showHints)
                                  Padding(
                                    padding: const EdgeInsets.only(bottom: 12.0),
                                    child: Text(
                                      '下一个目标数字：${gameProvider.nextTarget}',
                                      style: const TextStyle(fontSize: 22, fontWeight: FontWeight.bold, color: Colors.blue),
                                    ),
                                  ),
                              ],
                            );
                          },
                        ),
                        const SizedBox(height: 24),
                        Expanded(child: Center(child: SchulteGrid())),
                      ],
                    ),
                  ),
                );
              },
            ),
            if (_showCountdown)
              Positioned.fill(
                child: Container(
                  color: Colors.black.withOpacity(0.85),
                  child: Center(
                    child: Text(
                      '$_countdown',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 80,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  // 显示完成对话框
  void _showCompletionDialog(BuildContext context, GameProvider gameProvider) {
    // 防止多次显示对话框
    if (!gameProvider.isCompleted) return;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return CompletionDialog(gameProvider: gameProvider);
      },
    );

    // 不要在这里重置游戏状态，否则 elapsedTimeInMs 会被清零，导致完成时间为0
    // gameProvider.resetGame();
  }

  // 处理返回键
  Future<bool> _onWillPop() async {
    final gameProvider = Provider.of<GameProvider>(context, listen: false);

    // 如果游戏正在进行且未暂停，询问是否确认退出
    if (gameProvider.isPlaying &&
        !gameProvider.isPaused &&
        !gameProvider.isCompleted) {
      return await showDialog(
            context: context,
            builder:
                (context) => AlertDialog(
                  title: const Text('确认退出?'),
                  content: const Text('当前游戏进度将会丢失。'),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.of(context).pop(false),
                      child: const Text('取消'),
                    ),
                    ElevatedButton(
                      onPressed: () => Navigator.of(context).pop(true),
                      child: const Text('确认'),
                    ),
                  ],
                ),
          ) ??
          false;
    }

    return true;
  }
}
