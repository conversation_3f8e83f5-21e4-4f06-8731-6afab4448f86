import 'package:flutter/material.dart';
import 'package:focus_grid_client/constants/colors.dart';
import 'package:focus_grid_client/constants/game_constants.dart';
import 'package:focus_grid_client/models/game_record.dart';
import 'package:focus_grid_client/providers/stats_provider.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';

class StatsScreen extends StatefulWidget {
  const StatsScreen({super.key});

  @override
  State<StatsScreen> createState() => _StatsScreenState();
}

class _StatsScreenState extends State<StatsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  String _selectedMode = GameConstants.basicMode;
  int _selectedGridSize = GameConstants.defaultGridSize;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);

    // 加载游戏记录
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<StatsProvider>(context, listen: false).loadRecords();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('游戏统计'),
        centerTitle: true,
        bottom: TabBar(
          controller: _tabController,
          tabs: const [Tab(text: '记录'), Tab(text: '图表')],
        ),
      ),
      body: Consumer<StatsProvider>(
        builder: (context, statsProvider, child) {
          if (statsProvider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (statsProvider.records.isEmpty) {
            return const Center(
              child: Text('暂无游戏记录', style: TextStyle(fontSize: 18)),
            );
          }

          return Column(
            children: [
              // 过滤器
              Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    // 网格大小选择
                    DropdownButtonFormField<int>(
                      decoration: const InputDecoration(
                        labelText: '网格大小',
                        border: OutlineInputBorder(),
                      ),
                      value: _selectedGridSize,
                      items:
                          GameConstants.gridSizeOptions.map((size) {
                            return DropdownMenuItem(
                              value: size,
                              child: Text('${size}x${size}'),
                            );
                          }).toList(),
                      onChanged: (value) {
                        if (value != null) {
                          setState(() {
                            _selectedGridSize = value;
                          });
                        }
                      },
                    ),
                  ],
                ),
              ),

              // 记录摘要
              _buildStatsSummary(statsProvider),

              // Tab内容
              Expanded(
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    // 记录列表
                    _buildRecordsList(statsProvider),

                    // 图表
                    _buildChart(statsProvider),
                  ],
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  // 构建统计摘要
  Widget _buildStatsSummary(StatsProvider statsProvider) {
    final bestRecord = statsProvider.getBestRecord(
      _selectedMode,
      _selectedGridSize,
    );
    final averageTime = statsProvider.getAverageTime(
      _selectedMode,
      _selectedGridSize,
    );
    final recordCount =
        statsProvider
            .getFilteredRecords(_selectedMode, _selectedGridSize)
            .length;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  _buildStatItem(
                    title: '最佳成绩',
                    value: bestRecord != null ? bestRecord.formattedTime : '-',
                    icon: Icons.emoji_events,
                    color: Colors.amber,
                  ),
                  _buildStatItem(
                    title: '平均用时',
                    value:
                        averageTime != null
                            ? '${averageTime.inMinutes}:${(averageTime.inSeconds % 60).toString().padLeft(2, '0')}'
                            : '-',
                    icon: Icons.access_time,
                    color: AppColors.primaryColor,
                  ),
                  _buildStatItem(
                    title: '完成次数',
                    value: recordCount.toString(),
                    icon: Icons.checklist,
                    color: AppColors.correctColor,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 构建统计项
  Widget _buildStatItem({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Column(
      children: [
        Icon(icon, color: color, size: 28),
        const SizedBox(height: 4),
        Text(
          title,
          style: const TextStyle(fontSize: 12, color: AppColors.neutralColor),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
        ),
      ],
    );
  }

  // 构建记录列表
  Widget _buildRecordsList(StatsProvider statsProvider) {
    final filteredRecords = statsProvider.getFilteredRecords(
      _selectedMode,
      _selectedGridSize,
    );

    if (filteredRecords.isEmpty) {
      return const Center(
        child: Text('暂无符合条件的记录', style: TextStyle(fontSize: 16)),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: filteredRecords.length,
      itemBuilder: (context, index) {
        final record = filteredRecords[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: AppColors.primaryColor.withOpacity(0.2),
              child: Text(
                '${record.gridSize}',
                style: const TextStyle(
                  color: AppColors.primaryColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            title: Text(
              '完成时间: ${record.formattedTime}',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            subtitle: Text(
              '日期: ${record.formattedDate}',
              style: const TextStyle(fontSize: 12),
            ),
            trailing: _getModeBadge(record.mode),
          ),
        );
      },
    );
  }

  // 构建图表
  Widget _buildChart(StatsProvider statsProvider) {
    final chartData = statsProvider.getChartData(
      _selectedMode,
      _selectedGridSize,
    );

    if (chartData.isEmpty) {
      return const Center(
        child: Text('暂无足够数据生成图表', style: TextStyle(fontSize: 16)),
      );
    }

    // 转换为数据点列表
    final List<TimeData> dataPoints =
        chartData.map((entry) {
          return TimeData(
            entry.key,
            entry.value / 1000, // 毫秒转为秒
            DateFormat('MM-dd').format(entry.key),
          );
        }).toList();

    // 找出最大值和最小值，用于计算
    double maxValue = dataPoints
        .map((e) => e.seconds)
        .reduce((max, value) => max > value ? max : value);
    double minValue = dataPoints
        .map((e) => e.seconds)
        .reduce((min, value) => min < value ? min : value);

    // 如果最小值等于最大值，调整范围
    if (minValue == maxValue) {
      minValue = minValue * 0.8;
      maxValue = maxValue * 1.2;
    }

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          const Padding(
            padding: EdgeInsets.all(8.0),
            child: Text(
              '完成时间趋势 (秒)',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            child: Column(
              children: [
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.only(top: 16, right: 16),
                    child: CustomPaint(
                      size: Size.infinite,
                      painter: SimpleChartPainter(
                        dataPoints: dataPoints,
                        maxValue: maxValue,
                        minValue: minValue,
                        lineColor: AppColors.primaryColor,
                      ),
                    ),
                  ),
                ),
                // X轴标签
                SizedBox(
                  height: 40,
                  child: ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemCount: dataPoints.length,
                    itemBuilder: (context, index) {
                      // 只显示部分标签避免拥挤
                      if (index % 2 == 0 || index == dataPoints.length - 1) {
                        return Container(
                          width:
                              (MediaQuery.of(context).size.width - 60) /
                              dataPoints.length,
                          alignment: Alignment.center,
                          child: Text(
                            dataPoints[index].label,
                            style: const TextStyle(fontSize: 10),
                          ),
                        );
                      } else {
                        return Container(
                          width:
                              (MediaQuery.of(context).size.width - 60) /
                              dataPoints.length,
                        );
                      }
                    },
                  ),
                ),
              ],
            ),
          ),
          // 图例说明
          Container(
            padding: const EdgeInsets.only(top: 8.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(width: 12, height: 12, color: AppColors.primaryColor),
                const SizedBox(width: 8),
                const Text('完成时间 (秒)'),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // 获取模式标志
  Widget _getModeBadge(String mode) {
    String text;
    Color color;

    switch (mode) {
      case GameConstants.basicMode:
        text = '基础';
        color = AppColors.primaryColor;
        break;
      case GameConstants.challengeMode:
        text = '挑战';
        color = Colors.orange;
        break;
      case GameConstants.customMode:
        text = '自定义';
        color = Colors.purple;
        break;
      case GameConstants.relaxMode:
        text = '放松';
        color = Colors.teal;
        break;
      default:
        text = '未知';
        color = AppColors.neutralColor;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      decoration: BoxDecoration(
        color: color.withOpacity(0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
      ),
      child: Text(
        text,
        style: TextStyle(
          color: color,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }
}

// 数据点类
class TimeData {
  final DateTime date;
  final double seconds;
  final String label;

  TimeData(this.date, this.seconds, this.label);
}

// 简单图表绘制类
class SimpleChartPainter extends CustomPainter {
  final List<TimeData> dataPoints;
  final double maxValue;
  final double minValue;
  final Color lineColor;

  SimpleChartPainter({
    required this.dataPoints,
    required this.maxValue,
    required this.minValue,
    required this.lineColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint =
        Paint()
          ..color = lineColor
          ..style = PaintingStyle.stroke
          ..strokeWidth = 3.0;

    final dotPaint =
        Paint()
          ..color = lineColor
          ..style = PaintingStyle.fill;

    final path = Path();

    // 计算Y轴范围和缩放
    final double range = maxValue - minValue;
    final double heightRatio = size.height / (range > 0 ? range : 1);

    for (int i = 0; i < dataPoints.length; i++) {
      final double x = i * (size.width / (dataPoints.length - 1));
      final double normalizedY = dataPoints[i].seconds - minValue;
      final double y = size.height - normalizedY * heightRatio;

      // 绘制数据点
      canvas.drawCircle(Offset(x, y), 4, dotPaint);

      // 连线
      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }
    }

    canvas.drawPath(path, paint);

    // 绘制网格线
    final gridPaint =
        Paint()
          ..color = Colors.grey.withOpacity(0.3)
          ..style = PaintingStyle.stroke
          ..strokeWidth = 1.0;

    // 水平网格线
    for (int i = 0; i <= 5; i++) {
      final y = i * (size.height / 5);
      canvas.drawLine(Offset(0, y), Offset(size.width, y), gridPaint);
    }
  }

  @override
  bool shouldRepaint(SimpleChartPainter oldDelegate) =>
      oldDelegate.dataPoints != dataPoints ||
      oldDelegate.maxValue != maxValue ||
      oldDelegate.minValue != minValue ||
      oldDelegate.lineColor != lineColor;
}
